# 🎮 Prompt Arena Challenge

Un juego interactivo para mejorar tus habilidades con prompts de IA a través de desafíos divertidos y educativos.

## 🚀 Características Principales

### 🎯 Desafíos Incluidos

1. **📚 Información de Personajes** - Crea prompts efectivos para obtener información sobre personajes de Rick and Morty
2. **🤖 ¿Humano o IA?** - Identifica qué contenido fue creado por humanos y cuál por IA
3. **🔍 Cazador de Alucinaciones** - Encuentra funciones inventadas por la IA en código
4. **🎯 Elige el Mejor Prompt** - Selecciona los prompts más efectivos para diferentes tareas

### ✨ Características del Juego

- **Multijugador**: Hasta 10 participantes pueden unirse
- **Sistema de Puntuación**: Puntos basados en precisión y velocidad
- **Interfaz Moderna**: UI/UX optimizada con colores de alto contraste
- **Validación con IA Real**: Integración con Gemini API para evaluación inteligente
- **Control de Host**: Los anfitriones pueden terminar rondas temprano
- **Responsive**: Funciona en móviles, tablets y escritorio

## 🛠️ Tecnologías Utilizadas

- **React 18** + **TypeScript**
- **Vite** para desarrollo rápido
- **Tailwind CSS** para estilos
- **Shadcn/ui** para componentes
- **Google Gemini API** para validación de IA
- **Lucide React** para iconos

## 📋 Prerrequisitos

- Node.js 18+ o Bun
- Cuenta de Google AI Platform (para API key de Gemini)

## 🔧 Instalación

1. **Clona el repositorio**
```bash
git clone [repository-url]
cd prompt-arena-challenge
```

2. **Instala dependencias**
```bash
npm install
# o
bun install
```

3. **Configura variables de entorno**
```bash
cp .env.example .env
```

4. **Obtén tu API key de Gemini**
   - Ve a [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Crea una nueva API key
   - Reemplaza `your_gemini_api_key_here` en tu archivo `.env`

5. **Ejecuta el proyecto**
```bash
npm run dev
# o
bun dev
```

## 🎮 Cómo Jugar

### Para el Anfitrión
1. Accede a la aplicación y registra tu nombre
2. Como primer jugador, serás automáticamente el anfitrión
3. Espera a que otros jugadores se unan
4. Inicia los desafíos cuando todos estén listos
5. Puedes terminar rondas temprano usando el botón "Terminar Turno"

### Para los Participantes
1. Únete usando tu nombre
2. Espera en la sala hasta que el anfitrión inicie
3. Participa en cada desafío dentro del tiempo límite
4. Revisa tu puntuación en el tablero en vivo

## 🏆 Sistema de Puntuación

### Información de Personajes (Rick and Morty)
- **0-100 puntos** basados en calidad del prompt (evaluado por Gemini AI)
- Factores: claridad, especificidad, estructura

### ¿Humano o IA?
- **+20 puntos** por respuesta correcta
- **-5 puntos** por respuesta incorrecta

### Cazador de Alucinaciones
- **+25 puntos** por alucinación encontrada correctamente
- **-5 puntos** por falso positivo

### Elige el Mejor Prompt
- **+30 puntos** por prompt correcto
- **+25 puntos** por modelo correcto
- **-10 puntos** por respuesta incorrecta

## 🎨 Mejoras Implementadas

### ✅ Completadas
- ✅ **Esquema de colores mejorado** - Mayor contraste y legibilidad
- ✅ **Eliminación de spoilers** - Descriptions genéricas en registro
- ✅ **Desafíos más amigables** - Títulos y tareas humanizadas
- ✅ **Validación con IA real** - Integración con Gemini API
- ✅ **Cazador de alucinaciones mejorado** - Ejemplos más claros con GoogleAI
- ✅ **Reemplazo "mejor modelo" → "mejor prompt"** - Enfoque en calidad de prompts
- ✅ **Control de anfitrión** - Botón para terminar rondas temprano
- ✅ **Primer desafío Rick and Morty** - Cambio de Los Simpsons a Rick and Morty
- ✅ **Eliminación de "completar código"** - Removido completamente
- ✅ **3 desafíos principales** - Estructura optimizada

### 🎯 Enfoque en Educación
- **Prompts reales**: Tareas basadas en casos de uso reales
- **Retroalimentación inteligente**: Explicaciones detalladas
- **Ejemplos prácticos**: Código con bibliotecas reales (GoogleAI)
- **Consejos útiles**: Tips para identificar alucinaciones de IA

## 🧪 Desarrollo

### Comandos Disponibles
```bash
npm run dev          # Servidor de desarrollo
npm run build        # Build de producción
npm run preview      # Preview del build
npm run lint         # Linting con ESLint
```

### Estructura del Proyecto
```
src/
├── components/
│   ├── ChallengeArena.tsx     # Arena principal de desafíos
│   ├── Registration.tsx       # Registro de jugadores
│   ├── WaitingRoom.tsx       # Sala de espera
│   ├── FinalResults.tsx      # Resultados finales
│   └── challenges/
│       ├── PromptBattle.tsx   # Desafío de Rick and Morty
│       ├── HumanOrAI.tsx     # Detección humano vs IA
│       ├── ErrorDetector.tsx  # Cazador de alucinaciones
│       └── PromptChoice.tsx   # Selección de mejores prompts
├── pages/
│   └── Index.tsx             # Página principal con estados
└── lib/
    └── utils.ts              # Utilidades compartidas
```

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📝 Licencia

Este proyecto está bajo la licencia MIT. Ver `LICENSE` para más detalles.

## 🙏 Agradecimientos

- **Google AI** por la API de Gemini
- **Shadcn/ui** por los componentes
- **Rick and Morty API** por los datos de prueba
- **Tailwind CSS** por el sistema de diseño

---

*¡Diviértete mejorando tus habilidades con prompts de IA! 🚀*