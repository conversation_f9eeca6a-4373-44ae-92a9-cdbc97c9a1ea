import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Participant, Challenge } from '@/pages/Index';
import PromptBattle from '@/components/challenges/PromptBattle';
import HumanOrAI from '@/components/challenges/HumanOrAI';
import ErrorDetector from '@/components/challenges/ErrorDetector';
import PromptChoice from '@/components/challenges/PromptChoice';

interface ChallengeArenaProps {
  participants: Participant[];
  setParticipants: React.Dispatch<React.SetStateAction<Participant[]>>;
  onGameEnd: () => void;
  currentUser: Participant | null;
}

const challenges: Challenge[] = [
  {
    id: 'prompt',
    title: '📚 Información de Personajes',
    description: 'Obtén información sobre personajes de <PERSON> and <PERSON><PERSON><PERSON> usando prompts efectivos',
    timeLimit: 180,
    type: 'prompt'
  },
  {
    id: 'human-ai',
    title: '🤖 ¿Humano o IA?',
    description: 'Identifica qué contenido fue creado por humanos y cuál por IA',
    timeLimit: 180,
    type: 'human-ai'
  },
  {
    id: 'errors',
    title: '🔍 Cazador de Alucinaciones',
    description: 'Encuentra funciones y métodos inventados por la IA',
    timeLimit: 180,
    type: 'errors'
  },
  {
    id: 'prompt-choice',
    title: '🎯 Elige el Mejor Prompt',
    description: 'Selecciona los prompts más efectivos para obtener mejores resultados',
    timeLimit: 180,
    type: 'prompt-choice'
  }
];

const ChallengeArena = ({ participants, setParticipants, onGameEnd, currentUser }: ChallengeArenaProps) => {
  const [currentChallengeIndex, setCurrentChallengeIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(challenges[0].timeLimit);
  const [isActive, setIsActive] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const currentChallenge = challenges[currentChallengeIndex];
  const isLastChallenge = currentChallengeIndex === challenges.length - 1;

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => time - 1);
      }, 1000);
    } else if (isActive && timeLeft === 0) {
      setIsActive(false);
      setShowResults(true);
    }

    return () => clearInterval(interval);
  }, [isActive, timeLeft]);

  const startChallenge = () => {
    setTimeLeft(currentChallenge.timeLimit);
    setIsActive(true);
    setShowResults(false);
  };

  const nextChallenge = () => {
    if (isLastChallenge) {
      onGameEnd();
    } else {
      setCurrentChallengeIndex(prev => prev + 1);
      setShowResults(false);
      setIsActive(false);
      setTimeLeft(challenges[currentChallengeIndex + 1].timeLimit);
    }
  };

  const updateParticipantScore = (participantId: string, points: number) => {
    setParticipants(prev => 
      prev.map(p => 
        p.id === participantId 
          ? { ...p, score: p.score + points }
          : p
      )
    );
  };

  const endChallenge = () => {
    setIsActive(false);
    setTimeLeft(0);
    setShowResults(true);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = ((challenges[0].timeLimit - timeLeft) / challenges[0].timeLimit) * 100;

  const getBadgeColor = (position: number) => {
    if (position === 0) return 'bg-yellow-500';
    if (position === 1) return 'bg-gray-400';
    if (position === 2) return 'bg-amber-600';
    return 'bg-gray-600';
  };

  const sortedParticipants = [...participants].sort((a, b) => b.score - a.score);

  return (
    <div className="max-w-6xl mx-auto">
      {/* Challenge Header */}
      <Card className="mb-6 bg-game-card border-2 border-purple-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-4xl font-arcade text-neon-primary animate-neon-pulse">
                {currentChallenge.title}
              </CardTitle>
              <p className="text-high-contrast font-retro mt-3 text-xl">{currentChallenge.description}</p>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-6">
                <div>
                  <div className="timer-text text-5xl animate-text-glow">
                    {formatTime(timeLeft)}
                  </div>
                  <div className="text-readable text-lg">
                    ⚡ RONDA {currentChallengeIndex + 1} DE {challenges.length}
                  </div>
                </div>
                {currentUser?.isHost && isActive && (
                  <Button
                    onClick={endChallenge}
                    variant="outline"
                    size="sm"
                    className="border-2 border-red-500 text-red-400 hover:bg-red-500/20 font-bold text-ultra-contrast"
                  >
                    🛑 Terminar Turno
                  </Button>
                )}
              </div>
            </div>
          </div>
          <Progress 
            value={progressPercentage} 
            className="mt-4 h-4 bg-gray-700"
          />
          <div className="text-center mt-3">
            <span className="progress-text text-xl">
              PROGRESO: {Math.round(progressPercentage)}%
            </span>
          </div>
        </CardHeader>
      </Card>

      {/* Challenge Content */}
      <div className="mb-6">
        {!isActive && !showResults && (
          <Card className="bg-game-card border-2 border-cyan-500/40 backdrop-blur-sm shadow-2xl">
            <CardContent className="text-center py-12">
              <h3 className="text-3xl font-title text-cyan-400 mb-4 glow-accent text-ultra-contrast">
                ¿Listos para el desafío épico?
              </h3>
              <p className="text-gray-200 mb-6 font-semibold text-lg text-ultra-contrast">
                Tendrás {Math.floor(currentChallenge.timeLimit / 60)} minutos para completar: {currentChallenge.title}
              </p>
              <Button 
                onClick={startChallenge}
                className="h-14 px-8 text-xl btn-game-success hover:scale-105 transition-all duration-300 shadow-xl text-ultra-contrast"
              >
                🚀 COMENZAR DESAFÍO
              </Button>
            </CardContent>
          </Card>
        )}

        {isActive && (
          <div>
            {currentChallenge.type === 'prompt' && (
              <PromptBattle 
                timeLeft={timeLeft} 
                participants={participants}
                onUpdateScore={updateParticipantScore}
              />
            )}
            {currentChallenge.type === 'human-ai' && (
              <HumanOrAI 
                timeLeft={timeLeft} 
                participants={participants}
                onUpdateScore={updateParticipantScore}
              />
            )}
            {currentChallenge.type === 'errors' && (
              <ErrorDetector 
                timeLeft={timeLeft} 
                participants={participants}
                onUpdateScore={updateParticipantScore}
              />
            )}
            {currentChallenge.type === 'prompt-choice' && (
              <PromptChoice 
                timeLeft={timeLeft} 
                participants={participants}
                onUpdateScore={updateParticipantScore}
              />
            )}
          </div>
        )}

        {showResults && (
          <Card className="bg-game-card border-2 border-green-500/40 backdrop-blur-sm shadow-2xl">
            <CardContent className="text-center py-12">
              <h3 className="text-3xl font-title text-green-400 mb-4 text-success glow-text text-ultra-contrast">
                ¡Tiempo terminado!
              </h3>
              <p className="text-gray-200 mb-6 font-semibold text-lg text-ultra-contrast">
                Desafío completado. {isLastChallenge ? '¡Preparate para los resultados finales épicos!' : '¿Listos para el siguiente round?'}
              </p>
              <Button 
                onClick={nextChallenge}
                className="h-14 px-8 text-xl btn-game-primary hover:scale-105 transition-all duration-300 shadow-xl text-ultra-contrast"
              >
                {isLastChallenge ? '🏆 VER RESULTADOS FINALES' : '➡️ SIGUIENTE DESAFÍO'}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Live Scoreboard */}
      <Card className="bg-game-card border-2 border-yellow-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-title text-yellow-400 glow-text animate-pulse-glow text-ultra-contrast">
            🏆 Puntuación en Vivo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sortedParticipants.map((participant, index) => (
              <div 
                key={participant.id}
                className="participant-card flex items-center justify-between p-4 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold shadow-lg ${getBadgeColor(index)}`}>
                    {index + 1}
                  </div>
                  <span className="text-white font-bold text-lg text-ultra-contrast">{participant.name}</span>
                </div>
                <span className="score-display text-lg text-ultra-contrast">{participant.score} pts</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChallengeArena;
