
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface FinalResultsProps {
  participants: Participant[];
  onResetGame: () => void;
}

const FinalResults = ({ participants, onResetGame }: FinalResultsProps) => {
  const sortedParticipants = [...participants].sort((a, b) => b.score - a.score);
  const winner = sortedParticipants[0];
  const maxScore = 500; // 5 challenges × 100 max points each

  const getTrophyEmoji = (position: number) => {
    switch (position) {
      case 0: return '🏆';
      case 1: return '🥈';
      case 2: return '🥉';
      default: return '🏅';
    }
  };

  const getPositionColor = (position: number) => {
    switch (position) {
      case 0: return 'from-yellow-400 to-yellow-600';
      case 1: return 'from-gray-300 to-gray-500';
      case 2: return 'from-amber-500 to-amber-700';
      default: return 'from-gray-500 to-gray-700';
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Winner Announcement */}
      <Card className="bg-game-card border-2 border-yellow-500/50 backdrop-blur-sm shadow-2xl animate-glow">
        <CardContent className="text-center py-16">
          <div className="text-9xl mb-6 animate-pulse-glow">🎉</div>
          <h1 className="text-6xl font-arcade bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent mb-6 animate-neon-pulse">
            ¡FELICITACIONES!
          </h1>
          <p className="text-4xl text-high-contrast mb-6 font-arcade">
            🏆 <span className="status-winner animate-pulse-glow text-neon-accent">{winner?.name}</span> CONQUISTA LA BATALLA
          </p>
          <Badge className="bg-yellow-500 text-black text-2xl px-12 py-4 font-arcade shadow-xl animate-glow">
            ⚡ {winner?.score} PUNTOS TOTALES ⚡
          </Badge>
          <div className="mt-8 text-xl text-high-contrast font-retro">
            🎯 ¡EL MAESTRO SUPREMO DE LOS PROMPTS HA SIDO CORONADO!
          </div>
        </CardContent>
      </Card>

      {/* Podium */}
      <Card className="bg-game-card border-2 border-purple-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader>
          <CardTitle className="text-3xl font-title text-center text-purple-400 glow-text animate-pulse-glow">
            🏆 PODIUM FINAL ÉPICO
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {sortedParticipants.map((participant, index) => {
              const getBorderColor = () => {
                if (index === 0) return 'border-yellow-500/50 from-yellow-500/20 to-orange-500/20';
                if (index === 1) return 'border-gray-400/50 from-gray-400/20 to-gray-600/20';
                if (index === 2) return 'border-amber-500/50 from-amber-500/20 to-amber-700/20';
                return 'border-gray-600/50 from-gray-600/20 to-gray-800/20';
              };

              return (
                <div 
                  key={participant.id}
                  className={`participant-card flex items-center justify-between p-6 rounded-lg border-2 bg-gradient-to-r ${getBorderColor()} hover:scale-105 transition-all duration-300`}
                >
                  <div className="flex items-center space-x-6">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center text-3xl shadow-xl bg-gradient-to-r ${getPositionColor(index)}`}>
                      {getTrophyEmoji(index)}
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white">{participant.name}</h3>
                    <p className="text-gray-300 font-semibold">
                      🏅 Posición #{index + 1}
                      {participant.isHost && (
                        <Badge className="ml-2 bg-purple-500 text-white font-bold">👑 Host</Badge>
                      )}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="score-display text-2xl">
                    {participant.score}
                  </div>
                  <div className="text-sm text-gray-300 font-semibold">
                    {Math.round((participant.score / maxScore) * 100)}% efectividad
                  </div>
                </div>
              </div>
            )})}
          </div>
        </CardContent>
      </Card>

      {/* Battle Statistics */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="bg-game-card border-2 border-blue-500/40 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl text-blue-400 font-bold glow-accent">📊 Estadísticas de Batalla</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-200 font-semibold">Total de participantes:</span>
                <span className="text-white font-bold text-lg">{participants.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-200 font-semibold">Puntuación promedio:</span>
                <span className="text-white font-bold text-lg">
                  {Math.round(participants.reduce((sum, p) => sum + p.score, 0) / participants.length)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-200 font-semibold">Puntuación más alta:</span>
                <span className="text-warning font-bold text-lg">{winner?.score || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-200 font-semibold">Rondas completadas:</span>
                <span className="text-success font-bold text-lg">4/4 ✅</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-game-card border-2 border-green-500/40 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl text-green-400 font-bold glow-accent">🎯 Desafíos Completados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🎯</span>
                <span className="text-gray-200 font-semibold">Prompt Battle</span>
                <Badge className="bg-green-500 text-black ml-auto font-bold">✓</Badge>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🤖</span>
                <span className="text-gray-200 font-semibold">¿Humano o IA?</span>
                <Badge className="bg-green-500 text-black ml-auto font-bold">✓</Badge>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🔍</span>
                <span className="text-gray-200 font-semibold">Cazador de Errores</span>
                <Badge className="bg-green-500 text-black ml-auto font-bold">✓</Badge>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🎯</span>
                <span className="text-gray-200 font-semibold">Elección de Prompts</span>
                <Badge className="bg-green-500 text-black ml-auto font-bold">✓</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="text-center space-y-4">
        <Button 
          onClick={onResetGame}
          className="h-16 px-12 text-2xl btn-game-primary hover:scale-105 transition-all duration-300 shadow-2xl"
        >
          🚀 NUEVA BATALLA ÉPICA
        </Button>
        <p className="text-gray-200 text-lg font-semibold animate-pulse-glow">
          ¿Listos para otra ronda épica de desafíos de IA?
        </p>
      </div>

      {/* Victory Message */}
      <Card className="bg-game-card border-2 border-purple-500/40 backdrop-blur-sm shadow-2xl">
        <CardContent className="text-center py-8">
          <h3 className="text-3xl font-title text-purple-400 mb-4 glow-text">
            🎮 ¡Gracias por participar en la batalla!
          </h3>
          <p className="text-gray-200 text-lg leading-relaxed font-semibold text-readable">
            Has completado todos los desafíos de <span className="text-accent font-bold glow-accent">Prompt Battle</span>.
            <br />
            🧠 Esperamos que hayas aprendido nuevas técnicas para trabajar con IA.
            <br />
            <span className="text-warning font-bold animate-pulse-glow">⚡ ¡Comparte tus resultados y desafía a otros maestros de prompts! ⚡</span>
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default FinalResults;
