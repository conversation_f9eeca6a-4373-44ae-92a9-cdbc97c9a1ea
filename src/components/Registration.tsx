
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface RegistrationProps {
  onRegister: (name: string, multiplayer?: boolean) => void;
}

const Registration = ({ onRegister }: RegistrationProps) => {
  const [name, setName] = useState('');
  const [isMultiplayer, setIsMultiplayer] = useState(false);

  const handleSubmit = (e: React.FormEvent, multiplayer = false) => {
    e.preventDefault();
    if (name.trim()) {
      onRegister(name.trim(), multiplayer);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      <Card className="w-full max-w-md bg-game-card border-2 border-cyan-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-arcade text-neon-accent mb-4 animate-neon-pulse">
            🎯 ÚNETE A LA BATALLA
          </CardTitle>
          <p className="text-high-contrast font-retro text-lg">
            Ingresa tu nombre para comenzar la aventura épica
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <Input
                type="text"
                placeholder="Tu nombre de batalla épico..."
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="h-14 text-xl input-game"
                maxLength={20}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-arcade text-neon-accent text-center">
                ELIGE TU MODO DE BATALLA
              </h3>

              <div className="grid grid-cols-1 gap-4">
                <Button
                  onClick={(e) => handleSubmit(e, false)}
                  className="h-16 text-lg btn-game-primary hover:scale-105 transition-all duration-300"
                  disabled={!name.trim()}
                >
                  <div className="text-center">
                    <div className="text-xl">🎯 MODO SOLO</div>
                    <div className="text-sm opacity-80">Practica y mejora tus habilidades</div>
                  </div>
                </Button>

                <Button
                  onClick={(e) => handleSubmit(e, true)}
                  className="h-16 text-lg btn-game-secondary hover:scale-105 transition-all duration-300"
                  disabled={!name.trim()}
                >
                  <div className="text-center">
                    <div className="text-xl">⚔️ MODO MULTIJUGADOR</div>
                    <div className="text-sm opacity-80">Compite contra otros desarrolladores</div>
                  </div>
                </Button>
              </div>
            </div>
          </div>
          
          <div className="mt-8 p-6 bg-game-card-alt rounded-lg border-3 border-purple-500/40">
            <h3 className="text-xl font-arcade text-neon-accent mb-4 animate-text-glow">
              🏆 SOBRE LA BATALLA:
            </h3>
            <div className="text-readable space-y-3">
              <p className="text-lg">• 🧠 Desafíos únicos que pondrán a prueba tu dominio de IA</p>
              <p className="text-lg">• ⏱️ 3 minutos por ronda de máxima intensidad</p>
              <p className="text-lg">• 🎯 Puntos por velocidad y precisión</p>
              <p className="text-lg">• 🚀 ¡Aprende mientras compites contra otros desarrolladores!</p>
              <p className="text-lg">• 🏅 Demuestra que eres el maestro de los prompts</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Registration;
