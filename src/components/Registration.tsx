
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface RegistrationProps {
  onRegister: (name: string) => void;
}

const Registration = ({ onRegister }: RegistrationProps) => {
  const [name, setName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onRegister(name.trim());
    }
  };

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      <Card className="w-full max-w-md bg-game-card border-2 border-cyan-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-title text-cyan-400 mb-2 glow-accent text-ultra-contrast">
            🎯 Únete a la Batalla
          </CardTitle>
          <p className="text-gray-200 font-semibold text-ultra-contrast">
            Ingresa tu nombre para comenzar la aventura
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Input
                type="text"
                placeholder="Tu nombre de batalla..."
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="h-12 text-lg input-game text-white placeholder:text-gray-400 font-semibold text-ultra-contrast"
                maxLength={20}
              />
            </div>
            <Button 
              type="submit" 
              className="w-full h-12 text-lg btn-game-primary hover:scale-105 transition-all duration-300 text-ultra-contrast"
              disabled={!name.trim()}
            >
              🚀 ENTRAR AL BATTLE
            </Button>
          </form>
          
          <div className="mt-8 p-4 bg-game-card-alt rounded-lg border-2 border-purple-500/30">
            <h3 className="text-lg font-bold text-cyan-400 mb-3 text-emphasis glow-accent">
              🏆 Sobre la batalla:
            </h3>
            <div className="text-sm text-gray-200 space-y-2 text-readable font-semibold">
              <p>• 🧠 Desafíos únicos que pondrán a prueba tu dominio de IA</p>
              <p>• ⏱️ 3 minutos por ronda de máxima intensidad</p>
              <p>• 🎯 Puntos por velocidad y precisión</p>
              <p>• 🚀 ¡Aprende mientras compites contra otros desarrolladores!</p>
              <p>• 🏅 Demuestra que eres el maestro de los prompts</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Registration;
