
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface WaitingRoomProps {
  participants: Participant[];
  currentUser: Participant | null;
  onStartGame: () => void;
}

const WaitingRoom = ({ participants, currentUser, onStartGame }: WaitingRoomProps) => {
  const isHost = currentUser?.isHost;

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="bg-game-card border-2 border-cyan-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-title text-cyan-400 mb-2 glow-accent text-ultra-contrast">
            ⏳ Sala de Espera
          </CardTitle>
          <p className="text-gray-200 font-semibold text-ultra-contrast">
            <PERSON><PERSON>ando que todos los guerreros se unan a la batalla...
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            {/* Participants List */}
            <div>
              <h3 className="text-xl font-bold text-purple-400 mb-4 text-ultra-contrast glow-text">
                👥 Participantes ({participants.length})
              </h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {participants.map((participant, index) => (
                  <div 
                    key={participant.id}
                    className="participant-card flex items-center justify-between p-4 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                        {index + 1}
                      </div>
                      <span className="text-white font-bold text-lg text-ultra-contrast">{participant.name}</span>
                    </div>
                    <div className="flex space-x-2">
                      {participant.isHost && (
                        <Badge className="bg-yellow-500 text-black font-bold shadow-lg text-ultra-contrast">👑 Host</Badge>
                      )}
                      {participant.id === currentUser?.id && (
                        <Badge className="bg-green-500 text-white font-bold shadow-lg text-ultra-contrast">Tú</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Game Info */}
            <div>
              <h3 className="text-xl font-bold text-purple-400 mb-4 text-ultra-contrast glow-text">
                🎮 Información del Juego
              </h3>
              <div className="space-y-4">
                <div className="p-4 bg-game-card-alt rounded-lg border-2 border-cyan-500/30">
                  <h4 className="font-bold text-cyan-400 mb-3 text-ultra-contrast glow-accent">📋 Fases del Battle:</h4>
                  <div className="space-y-3 text-sm text-gray-200 font-semibold">
                    <div className="flex justify-between items-center">
                      <span className="text-ultra-contrast">🎯 Ronda 1: Maestría de Prompts</span>
                      <span className="timer-text text-base text-ultra-contrast">3 min</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-ultra-contrast">🤖 Ronda 2: ¿Humano o IA?</span>
                      <span className="timer-text text-base text-ultra-contrast">3 min</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-ultra-contrast">🔍 Ronda 3: Cazador de Errores</span>
                      <span className="timer-text text-base text-ultra-contrast">3 min</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-ultra-contrast">🏅 Ronda 4: Elección Perfecta</span>
                      <span className="timer-text text-base text-ultra-contrast">3 min</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-gradient-to-r from-purple-800/30 to-pink-800/30 rounded-lg border-2 border-purple-500/40">
                  <h4 className="font-bold text-pink-400 mb-2 text-ultra-contrast glow-accent">🏆 Sistema de Puntos:</h4>
                  <p className="text-sm text-gray-200 font-semibold text-ultra-contrast">
                    🚀 Cada ronda otorga puntos basados en <span className="text-success text-ultra-contrast">precisión</span> y <span className="text-warning text-ultra-contrast">velocidad</span>.<br/>
                    ⚡ ¡El guerrero con más puntos conquista la batalla épica!
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Start Game Button */}
          {isHost && (
            <div className="mt-8 text-center">
              <Button 
                onClick={onStartGame}
                className="h-14 px-8 text-xl btn-game-success hover:scale-105 transition-all duration-300 shadow-xl text-ultra-contrast"
                disabled={participants.length < 1}
              >
                🚀 INICIAR BATALLA ÉPICA
              </Button>
              {participants.length < 2 && (
                <p className="text-warning mt-3 text-base font-bold animate-pulse-glow text-ultra-contrast">
                  💡 Consejo: La batalla es más épica con más participantes
                </p>
              )}
            </div>
          )}

          {!isHost && (
            <div className="mt-8 text-center p-4 bg-game-card-alt rounded-lg border-2 border-yellow-500/40">
              <p className="text-gray-200 font-semibold text-lg text-ultra-contrast">
                ⏳ Esperando que <span className="text-warning font-bold glow-text text-ultra-contrast">
                  {participants.find(p => p.isHost)?.name}
                </span> inicie la batalla épica...
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WaitingRoom;
