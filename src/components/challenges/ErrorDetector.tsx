import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface ErrorDetectorProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const buggyCode = `// Sistema de desarrollo con IA - Código generado por ChatGPT
// Este código muestra funciones "inventadas" típicas de alucinaciones de IA
import { OpenAI } from 'openai';
import { CodeAnalyzer } from 'ai-code-analyzer';

class SmartDeveloper {
  constructor() {
    this.ai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    this.analyzer = new CodeAnalyzer();
  }

  // Función para auto-completar código usando IA
  async autoCompleteCode(partialCode) {
    // ❌ ALUCINACIÓN: Esta función no existe en OpenAI API
    return await this.ai.completeCodeIntelligently(partialCode, {
      language: 'javascript',
      style: 'professional',
      addComments: true
    });
  }

  // Función para detectar bugs automáticamente
  async findAllBugs(sourceCode) {
    // ❌ ALUCINACIÓN: Función inventada que suena plausible
    const bugs = await this.analyzer.detectBugsWithAI(sourceCode);

    // ❌ ALUCINACIÓN: Método que no existe
    const severity = await this.analyzer.calculateBugSeverity(bugs);

    return { bugs, severity };
  }

  // Función para optimizar código automáticamente
  async optimizePerformance(code) {
    // ❌ ALUCINACIÓN: Funcionalidad que no existe
    const optimized = await this.ai.optimizeCodePerformance(code, {
      target: 'speed',
      preserveLogic: true,
      addBenchmarks: true
    });

    return optimized;
  }

  // Función para generar tests automáticamente
  async generateUnitTests(functionCode) {
    // ❌ ALUCINACIÓN: Método inventado
    return await this.ai.createComprehensiveTests(functionCode, {
      framework: 'jest',
      coverage: 100,
      edgeCases: true,
      mockDependencies: true
    });
  }

  // Función para documentar código automáticamente
  async generateDocumentation(codebase) {
    // ❌ ALUCINACIÓN: Función que no existe en APIs reales
    const docs = await this.ai.generateFullDocumentation(codebase, {
      format: 'markdown',
      includeExamples: true,
      generateDiagrams: true,
      multilingual: ['es', 'en']
    });

    return docs;
  }
}`;

const errors = [
  {
    line: 27,
    error: "OpenAI.completeCodeIntelligently() no existe",
    explanation: "Esta función fue inventada por la IA. OpenAI no tiene un método específico llamado 'completeCodeIntelligently'.",
    severity: "crítico"
  },
  {
    line: 37,
    error: "CodeAnalyzer.detectBugsWithAI() no existe",
    explanation: "Función alucinada. No existe una biblioteca llamada 'ai-code-analyzer' con este método.",
    severity: "alto"
  },
  {
    line: 40,
    error: "CodeAnalyzer.calculateBugSeverity() no existe",
    explanation: "Método inventado. Esta funcionalidad específica no existe en bibliotecas reales.",
    severity: "alto"
  },
  {
    line: 48,
    error: "OpenAI.optimizeCodePerformance() no existe",
    explanation: "Función alucinada. OpenAI no proporciona optimización automática de código con estos parámetros.",
    severity: "crítico"
  },
  {
    line: 60,
    error: "OpenAI.createComprehensiveTests() no existe",
    explanation: "Método inventado. OpenAI no tiene una función específica para generar tests con estas opciones.",
    severity: "alto"
  },
  {
    line: 71,
    error: "OpenAI.generateFullDocumentation() no existe",
    explanation: "Función alucinada. No existe un método específico en OpenAI para generar documentación completa.",
    severity: "medio"
  }
];

const ErrorDetector = ({ timeLeft, participants, onUpdateScore }: ErrorDetectorProps) => {
  const [foundErrors, setFoundErrors] = useState<number[]>([]);
  const [submitted, setSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const toggleError = (lineNumber: number) => {
    if (!submitted) {
      setFoundErrors(prev => 
        prev.includes(lineNumber)
          ? prev.filter(line => line !== lineNumber)
          : [...prev, lineNumber]
      );
    }
  };

  const handleSubmit = () => {
    setSubmitted(true);
    setTimeout(() => setShowResults(true), 1000);
    
    // Calculate score based on correctly identified errors
    const correctErrors = foundErrors.filter(line => 
      errors.some(error => error.line === line)
    );
    const incorrectErrors = foundErrors.filter(line => 
      !errors.some(error => error.line === line)
    );
    
    const points = Math.max(0, correctErrors.length * 25 - incorrectErrors.length * 5);
    onUpdateScore(participants[0]?.id || '', points);
  };

  const getCodeLines = () => {
    return buggyCode.split('\n').map((line, index) => ({
      number: index + 1,
      content: line,
      hasError: errors.some(error => error.line === index + 1),
      isSelected: foundErrors.includes(index + 1)
    }));
  };

  const getCorrectFinds = () => {
    return foundErrors.filter(line => 
      errors.some(error => error.line === line)
    ).length;
  };

  return (
    <div className="space-y-6">
      <Card className="bg-game-card border-error/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-error font-gaming">
            🔍 Cazador de Alucinaciones de IA
          </CardTitle>
          <div className="space-y-2 text-ultra-contrast">
            <p>
              Una IA generó este código que supuestamente usa la biblioteca "GoogleAI" para análisis de usuarios.
            </p>
            <p className="text-sm bg-warning/20 p-3 rounded border border-warning/30 text-ultra-contrast">
              💡 <strong>Contexto:</strong> Las IAs a menudo "alucinas" funciones que suenan plausibles pero no existen realmente. 
              Tu misión es encontrar las funciones inventadas.
            </p>
          </div>
        </CardHeader>
      </Card>

      {/* Code with line numbers */}
      <Card className="bg-game-card border-accent/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-accent font-gaming">
            📝 Código a Analizar
          </CardTitle>
          <p className="text-ultra-contrast text-sm">
            Haz clic en las líneas donde creas que hay funciones inventadas por la IA
          </p>
        </CardHeader>
        <CardContent>
          <div className="bg-game-card-alt rounded-lg border border-accent/20 overflow-hidden">
            {getCodeLines().map((line) => (
              <div
                key={line.number}
                className={`flex hover:bg-accent/10 cursor-pointer transition-colors ${
                  line.isSelected ? 'bg-error/30 border-l-4 border-error' : ''
                } ${
                  showResults && line.hasError && line.isSelected
                    ? 'bg-success/30 border-l-4 border-success' 
                    : ''
                } ${
                  showResults && line.hasError && !line.isSelected
                    ? 'bg-error/20 border-l-4 border-error'
                    : ''
                } ${
                  showResults && line.isSelected && !line.hasError
                    ? 'bg-warning/20 border-l-4 border-warning'
                    : ''
                }`}
                onClick={() => toggleError(line.number)}
              >
                <div className="w-12 flex-shrink-0 text-muted text-sm text-right pr-4 py-2 bg-background/50 border-r border-accent/30 text-ultra-contrast">
                  {line.number}
                </div>
                <div className="flex-1 px-4 py-2">
                  <code className="text-sm text-ultra-contrast font-mono whitespace-pre">
                    {line.content || ' '}
                  </code>
                </div>
                {line.isSelected && (
                  <div className="flex-shrink-0 px-4 py-2 text-error">
                    🔍
                  </div>
                )}
                {showResults && line.hasError && line.isSelected && (
                  <div className="flex-shrink-0 px-4 py-2 text-success">
                    ✅
                  </div>
                )}
                {showResults && line.hasError && !line.isSelected && (
                  <div className="flex-shrink-0 px-4 py-2 text-error">
                    ❌
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 flex justify-between items-center">
            <div className="text-sm text-ultra-contrast">
              Funciones sospechosas marcadas: {foundErrors.length}
            </div>
            {!submitted && (
              <Button 
                onClick={handleSubmit}
                disabled={foundErrors.length === 0 || timeLeft === 0}
                className="btn-game-success animate-glow text-ultra-contrast"
              >
                🚀 Enviar Análisis
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {showResults && (
        <div className="space-y-4">
          <Card className="bg-game-card border-success/30">
            <CardContent className="text-center py-6">
              <h3 className="text-ultra-contrast text-success font-gaming mb-2 animate-glow">
                📊 Resultados del Análisis
              </h3>
              <p className="text-xl text-ultra-contrast mb-2">
                {getCorrectFinds()} de {errors.length} alucinaciones encontradas correctamente
              </p>
              <Badge className="bg-success text-success-foreground text-lg font-semibold animate-pulse-glow text-ultra-contrast">
                +{Math.max(0, getCorrectFinds() * 25 - (foundErrors.length - getCorrectFinds()) * 5)} puntos
              </Badge>
            </CardContent>
          </Card>

          {/* Error Details */}
          <Card className="bg-game-card border-primary/30">
            <CardHeader>
              <CardTitle className="text-game-subtitle text-primary font-gaming">
                🎭 Alucinaciones de IA Detectadas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {errors.map((error) => {
                  let severityColor = 'bg-warning';
                  if (error.severity === 'crítico') severityColor = 'bg-error';
                  else if (error.severity === 'alto') severityColor = 'bg-destructive';
                  
                  return (
                    <div 
                      key={`error-${error.line}`}
                      className={`p-4 rounded-lg border ${
                        foundErrors.includes(error.line)
                          ? 'bg-success/20 border-success/30'
                          : 'bg-error/20 border-error/30'
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-semibold text-accent">
                          Línea {error.line}
                        </span>
                        <div className="flex space-x-2">
                          <Badge className={`${severityColor} text-background font-semibold`}>
                            {error.severity}
                          </Badge>
                          {foundErrors.includes(error.line) && (
                            <Badge className="bg-success text-success-foreground animate-glow">
                              ✅ Encontrado
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-readable font-medium mb-1">{error.error}</p>
                      <p className="text-high-contrast text-sm">{error.explanation}</p>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tips */}
      <Card className="bg-game-card border-warning/30">
        <CardContent className="pt-6">
          <h3 className="font-gaming font-semibold text-warning mb-2 animate-glow">💡 Cómo identificar alucinaciones de IA en código:</h3>
          <ul className="text-sm text-high-contrast space-y-1">
            <li>• <strong>Métodos que suenan "demasiado inteligentes"</strong> - como completeCodeIntelligently()</li>
            <li>• <strong>Funciones "todo-en-uno" para desarrollo</strong> - como optimizeCodePerformance()</li>
            <li>• <strong>Bibliotecas inventadas</strong> - como 'ai-code-analyzer' que no existen</li>
            <li>• <strong>Métodos con parámetros muy específicos</strong> - que no aparecen en documentación oficial</li>
            <li>• <strong>Funcionalidades automáticas complejas</strong> - como generateFullDocumentation()</li>
            <li>• <strong>Siempre verificar la documentación oficial</strong> - de OpenAI, Google AI, etc.</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default ErrorDetector;
