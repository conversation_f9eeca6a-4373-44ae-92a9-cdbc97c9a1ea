import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface PromptChoiceProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const scenarios = [
  {
    id: 1,
    task: "Quieres que la IA escriba un email profesional para solicitar una reunión con tu jefe",
    prompts: [
      "Escribe un email",
      "Redacta un email profesional para solicitar una reunión con mi jefe sobre mi proyecto actual. Debe ser cordial, específico en cuanto al propósito, y sugerir horarios flexibles.",
      "Haz un email a mi jefe pidiendo reunión",
      "Email profesional: reunión jefe, proyecto, horarios"
    ],
    bestChoice: "Redacta un email profesional para solicitar una reunión con mi jefe sobre mi proyecto actual. Debe ser cordial, específico en cuanto al propósito, y sugerir horarios flexibles.",
    explanation: "Este prompt es específico, incluye contexto, tono deseado y detalles importantes. Los prompts vagos producen resultados genéricos."
  },
  {
    id: 2,
    task: "Necesitas que la IA te ayude a debuggear un error en tu código JavaScript",
    prompts: [
      "Mi código no funciona, ayúdame",
      "Tengo un error en JavaScript. Adjunto el código: [código]. El error que obtengo es: [mensaje de error]. ¿Puedes identificar el problema y explicar cómo solucionarlo?",
      "Error en JS, por favor arregla",
      "Debug este código JavaScript que falla"
    ],
    bestChoice: "Tengo un error en JavaScript. Adjunto el código: [código]. El error que obtengo es: [mensaje de error]. ¿Puedes identificar el problema y explicar cómo solucionarlo?",
    explanation: "Proporciona contexto específico, incluye el código y el error, y solicita tanto la solución como la explicación."
  },
  {
    id: 3,
    task: "Quieres que la IA te genere ideas creativas para una campaña de marketing",
    prompts: [
      "Dame ideas de marketing",
      "Genera 5 ideas creativas para una campaña de marketing digital dirigida a millennials interesados en tecnología sostenible. El presupuesto es limitado y queremos usar principalmente redes sociales.",
      "Ideas para campaña publicitaria",
      "Marketing creativo para jóvenes y tecnología"
    ],
    bestChoice: "Genera 5 ideas creativas para una campaña de marketing digital dirigida a millennials interesados en tecnología sostenible. El presupuesto es limitado y queremos usar principalmente redes sociales.",
    explanation: "Especifica cantidad, audiencia target, nicho, restricciones de presupuesto y canales preferidos. Más específico = mejores resultados."
  }
];

const PromptChoice = ({ timeLeft, participants, onUpdateScore }: PromptChoiceProps) => {
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: string }>({});
  const [submitted, setSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const handleAnswer = (scenarioId: number, prompt: string) => {
    if (!submitted) {
      setSelectedAnswers(prev => ({ ...prev, [scenarioId]: prompt }));
    }
  };

  const handleSubmit = () => {
    setSubmitted(true);
    setTimeout(() => setShowResults(true), 1000);
    
    // Calculate score
    let correctAnswers = 0;
    scenarios.forEach(scenario => {
      if (selectedAnswers[scenario.id] === scenario.bestChoice) {
        correctAnswers++;
      }
    });
    
    const points = correctAnswers * 30; // Higher points since fewer scenarios
    onUpdateScore(participants[0]?.id || '', points);
  };

  const getCorrectAnswers = () => {
    return scenarios.filter(scenario => selectedAnswers[scenario.id] === scenario.bestChoice).length;
  };

  return (
    <div className="space-y-6">
      <Card className="bg-game-card border-primary/30">
        <CardHeader>
          <CardTitle className="text-game-title text-primary font-gaming">
            ✨ Elige el Mejor Prompt
          </CardTitle>
          <p className="text-readable">
            Para cada tarea, selecciona el prompt que producirá el mejor resultado con IA
          </p>
        </CardHeader>
      </Card>

      <div className="grid gap-6">
        {scenarios.map((scenario) => (
          <Card key={scenario.id} className="bg-game-card border-secondary/30">
            <CardHeader>
              <div className="flex justify-between items-start">
                <CardTitle className="text-game-subtitle text-secondary font-gaming flex-1">
                  Tarea #{scenario.id}
                </CardTitle>
                {showResults && (
                  <Badge className={selectedAnswers[scenario.id] === scenario.bestChoice ? 'bg-success text-success-foreground animate-glow' : 'bg-error text-error-foreground'}>
                    {selectedAnswers[scenario.id] === scenario.bestChoice ? '✅ Correcto' : '❌ Incorrecto'}
                  </Badge>
                )}
              </div>
              <p className="text-readable text-sm mt-2 bg-game-card-alt p-3 rounded border-l-4 border-accent">
                <strong className="text-accent">Objetivo:</strong> {scenario.task}
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3 mb-4">
                {scenario.prompts.map((prompt, index) => (
                  <Button
                    key={`${scenario.id}-${index}`}
                    variant={selectedAnswers[scenario.id] === prompt ? "default" : "outline"}
                    onClick={() => handleAnswer(scenario.id, prompt)}
                    disabled={submitted}
                    className={`h-auto p-4 text-left justify-start ${
                      selectedAnswers[scenario.id] === prompt 
                        ? 'btn-game-secondary' 
                        : 'border-border hover:bg-muted/50'
                    }`}
                  >
                    <div className="w-full">
                      <div className="font-semibold text-readable mb-1">Opción {String.fromCharCode(65 + index)}</div>
                      <div className="text-sm text-high-contrast italic">"{prompt}"</div>
                    </div>
                  </Button>
                ))}
              </div>
              
              {showResults && (
                <div className="mt-4 p-4 bg-game-card-alt rounded-lg border border-accent/30">
                  <p className="text-sm text-readable mb-2">
                    <span className="font-semibold text-secondary">Mejor prompt:</span>
                  </p>
                  <p className="text-sm text-success italic mb-3">"{scenario.bestChoice}"</p>
                  <p className="text-xs text-high-contrast">
                    <span className="font-semibold">¿Por qué?</span> {scenario.explanation}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {!submitted && Object.keys(selectedAnswers).length === scenarios.length && (
        <div className="text-center">
          <Button 
            onClick={handleSubmit}
            className="btn-game-success h-12 px-8 text-lg font-bold animate-glow"
          >
            🚀 Enviar Respuestas
          </Button>
        </div>
      )}

      {showResults && (
        <Card className="bg-game-card border-success/30">
          <CardContent className="text-center py-6">
            <h3 className="text-game-title text-success font-gaming mb-2 animate-glow">
              📊 Resultados
            </h3>
            <p className="text-xl text-readable mb-2">
              {getCorrectAnswers()} de {scenarios.length} correctas
            </p>
            <Badge className="bg-success text-success-foreground text-lg font-semibold animate-pulse-glow">
              +{getCorrectAnswers() * 30} puntos
            </Badge>
          </CardContent>
        </Card>
      )}

      {/* Tips */}
      <Card className="bg-game-card border-warning/30">
        <CardContent className="pt-6">
          <h3 className="font-gaming font-semibold text-warning mb-2 animate-glow">💡 Tips para crear mejores prompts:</h3>
          <ul className="text-sm text-high-contrast space-y-1">
            <li>• <strong>Sé específico:</strong> Incluye detalles importantes y contexto</li>
            <li>• <strong>Define el formato:</strong> Especifica cómo quieres la respuesta</li>
            <li>• <strong>Proporciona ejemplos:</strong> Ayuda a la IA a entender lo que buscas</li>
            <li>• <strong>Establece restricciones:</strong> Menciona limitaciones importantes</li>
            <li>• <strong>Pide explicaciones:</strong> No solo la solución, sino el razonamiento</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default PromptChoice;
