import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface PromptChoiceProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const scenarios = [
  {
    id: 1,
    task: "Generar una función JavaScript para validar emails con regex",
    prompts: [
      "Crea una función para validar emails",
      "Crea una función JavaScript llamada 'validateEmail' que tome un string como parámetro y retorne true/false. Debe validar formato de email usando regex, manejar casos edge como emails vacíos, y incluir comentarios explicativos. Proporciona también 3 ejemplos de uso.",
      "Función de validación de email",
      "Valida emails en JavaScript"
    ],
    bestChoice: "Crea una función JavaScript llamada 'validateEmail' que tome un string como parámetro y retorne true/false. Debe validar formato de email usando regex, manejar casos edge como emails vacíos, y incluir comentarios explicativos. Proporciona también 3 ejemplos de uso.",
    explanation: "Especifica el nombre de la función, parámetros, tipo de retorno, implementación técnica y ejemplos prácticos."
  },
  {
    id: 2,
    task: "Debuggear un error de memoria en Python que causa memory leak",
    prompts: [
      "Mi código Python tiene problemas de memoria",
      "Actúa como un senior Python developer. Analiza este código que tiene un memory leak: [código]. Identifica las causas probables, explica por qué ocurre el problema, proporciona 3 soluciones específicas con código, y sugiere herramientas para profiling de memoria.",
      "Ayuda con memory leak en Python",
      "Optimiza el uso de memoria de este código"
    ],
    bestChoice: "Actúa como un senior Python developer. Analiza este código que tiene un memory leak: [código]. Identifica las causas probables, explica por qué ocurre el problema, proporciona 3 soluciones específicas con código, y sugiere herramientas para profiling de memoria.",
    explanation: "Define rol experto, tipo específico de problema, estructura de análisis y soluciones concretas con herramientas."
  },
  {
    id: 3,
    task: "Crear tests unitarios completos para una clase TypeScript",
    prompts: [
      "Haz tests para esta clase TypeScript",
      "Genera tests unitarios completos para esta clase TypeScript usando Jest. Incluye: setup/teardown, tests para todos los métodos públicos, casos edge, mocks para dependencias, assertions específicas, y cobertura de al menos 90%. Organiza en describe/it blocks.",
      "Necesito tests unitarios con Jest",
      "Tests para TypeScript con buena cobertura"
    ],
    bestChoice: "Genera tests unitarios completos para esta clase TypeScript usando Jest. Incluye: setup/teardown, tests para todos los métodos públicos, casos edge, mocks para dependencias, assertions específicas, y cobertura de al menos 90%. Organiza en describe/it blocks.",
    explanation: "Especifica framework de testing, estructura de tests, cobertura objetivo y organización del código de pruebas."
  },
  {
    id: 4,
    task: "Optimizar una consulta SQL que tarda más de 30 segundos",
    prompts: [
      "Esta consulta SQL es muy lenta",
      "Como DBA senior, analiza esta consulta SQL que tarda 30+ segundos: [query]. Identifica cuellos de botella, sugiere índices específicos, reescribe la query optimizada, explica el plan de ejecución, y proporciona métricas de mejora esperadas.",
      "Optimiza esta query SQL lenta",
      "Mejora el performance de esta consulta"
    ],
    bestChoice: "Como DBA senior, analiza esta consulta SQL que tarda 30+ segundos: [query]. Identifica cuellos de botella, sugiere índices específicos, reescribe la query optimizada, explica el plan de ejecución, y proporciona métricas de mejora esperadas.",
    explanation: "Establece contexto de performance, rol experto, análisis técnico específico y métricas cuantificables."
  },
  {
    id: 5,
    task: "Documentar una API REST en Node.js con todos los endpoints",
    prompts: [
      "Documenta esta API REST",
      "Crea documentación completa para esta API REST en Node.js: [código]. Incluye: descripción de cada endpoint, métodos HTTP, parámetros requeridos/opcionales, ejemplos de request/response en JSON, códigos de estado HTTP, y casos de error. Usa formato Markdown.",
      "Necesito docs para mi API",
      "Documentación de endpoints REST"
    ],
    bestChoice: "Crea documentación completa para esta API REST en Node.js: [código]. Incluye: descripción de cada endpoint, métodos HTTP, parámetros requeridos/opcionales, ejemplos de request/response en JSON, códigos de estado HTTP, y casos de error. Usa formato Markdown.",
    explanation: "Define estructura específica, formato de salida, y todos los elementos técnicos necesarios para documentación profesional."
  }
];

const PromptChoice = ({ timeLeft, participants, onUpdateScore }: PromptChoiceProps) => {
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: string }>({});
  const [submitted, setSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const handleAnswer = (scenarioId: number, prompt: string) => {
    if (!submitted) {
      setSelectedAnswers(prev => ({ ...prev, [scenarioId]: prompt }));
    }
  };

  const handleSubmit = () => {
    setSubmitted(true);
    setTimeout(() => setShowResults(true), 1000);
    
    // Calculate score
    let correctAnswers = 0;
    scenarios.forEach(scenario => {
      if (selectedAnswers[scenario.id] === scenario.bestChoice) {
        correctAnswers++;
      }
    });
    
    const points = correctAnswers * 30; // Higher points since fewer scenarios
    onUpdateScore(participants[0]?.id || '', points);
  };

  const getCorrectAnswers = () => {
    return scenarios.filter(scenario => selectedAnswers[scenario.id] === scenario.bestChoice).length;
  };

  return (
    <div className="space-y-6">
      <Card className="bg-game-card border-primary/30">
        <CardHeader>
          <CardTitle className="text-game-title text-primary font-gaming">
            ✨ Elige el Mejor Prompt
          </CardTitle>
          <p className="text-readable">
            Para cada tarea, selecciona el prompt que producirá el mejor resultado con IA
          </p>
        </CardHeader>
      </Card>

      <div className="grid gap-6">
        {scenarios.map((scenario) => (
          <Card key={scenario.id} className="bg-game-card border-secondary/30">
            <CardHeader>
              <div className="flex justify-between items-start">
                <CardTitle className="text-game-subtitle text-secondary font-gaming flex-1">
                  Tarea #{scenario.id}
                </CardTitle>
                {showResults && (
                  <Badge className={selectedAnswers[scenario.id] === scenario.bestChoice ? 'bg-success text-success-foreground animate-glow' : 'bg-error text-error-foreground'}>
                    {selectedAnswers[scenario.id] === scenario.bestChoice ? '✅ Correcto' : '❌ Incorrecto'}
                  </Badge>
                )}
              </div>
              <p className="text-readable text-sm mt-2 bg-game-card-alt p-3 rounded border-l-4 border-accent">
                <strong className="text-accent">Objetivo:</strong> {scenario.task}
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3 mb-4">
                {scenario.prompts.map((prompt, index) => (
                  <Button
                    key={`${scenario.id}-${index}`}
                    variant={selectedAnswers[scenario.id] === prompt ? "default" : "outline"}
                    onClick={() => handleAnswer(scenario.id, prompt)}
                    disabled={submitted}
                    className={`h-auto p-4 text-left justify-start ${
                      selectedAnswers[scenario.id] === prompt 
                        ? 'btn-game-secondary' 
                        : 'border-border hover:bg-muted/50'
                    }`}
                  >
                    <div className="w-full">
                      <div className="font-semibold text-readable mb-1">Opción {String.fromCharCode(65 + index)}</div>
                      <div className="text-sm text-high-contrast italic">"{prompt}"</div>
                    </div>
                  </Button>
                ))}
              </div>
              
              {showResults && (
                <div className="mt-4 p-4 bg-game-card-alt rounded-lg border border-accent/30">
                  <p className="text-sm text-readable mb-2">
                    <span className="font-semibold text-secondary">Mejor prompt:</span>
                  </p>
                  <p className="text-sm text-success italic mb-3">"{scenario.bestChoice}"</p>
                  <p className="text-xs text-high-contrast">
                    <span className="font-semibold">¿Por qué?</span> {scenario.explanation}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {!submitted && Object.keys(selectedAnswers).length === scenarios.length && (
        <div className="text-center">
          <Button 
            onClick={handleSubmit}
            className="btn-game-success h-12 px-8 text-lg font-bold animate-glow"
          >
            🚀 Enviar Respuestas
          </Button>
        </div>
      )}

      {showResults && (
        <Card className="bg-game-card border-success/30">
          <CardContent className="text-center py-6">
            <h3 className="text-game-title text-success font-gaming mb-2 animate-glow">
              📊 Resultados
            </h3>
            <p className="text-xl text-readable mb-2">
              {getCorrectAnswers()} de {scenarios.length} correctas
            </p>
            <Badge className="bg-success text-success-foreground text-lg font-semibold animate-pulse-glow">
              +{getCorrectAnswers() * 30} puntos
            </Badge>
          </CardContent>
        </Card>
      )}

      {/* Tips */}
      <Card className="bg-game-card border-warning/30">
        <CardContent className="pt-6">
          <h3 className="font-gaming font-semibold text-warning mb-2 animate-glow">💡 Tips para prompts de programación:</h3>
          <ul className="text-sm text-high-contrast space-y-1">
            <li>• <strong>Especifica el lenguaje y framework:</strong> JavaScript, Python, React, etc.</li>
            <li>• <strong>Define el contexto técnico:</strong> Tipo de aplicación, arquitectura, restricciones</li>
            <li>• <strong>Incluye el código problemático:</strong> Para debugging y optimización</li>
            <li>• <strong>Pide estructura específica:</strong> Funciones, clases, tests, documentación</li>
            <li>• <strong>Solicita explicaciones técnicas:</strong> Por qué funciona, mejores prácticas</li>
            <li>• <strong>Menciona herramientas:</strong> Jest, ESLint, frameworks específicos</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default PromptChoice;
