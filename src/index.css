@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    /* Enhanced gaming-themed light mode colors with better contrast */
    --background: 210 20% 98%;
    --foreground: 220 25% 12%;

    --card: 210 15% 96%;
    --card-foreground: 220 25% 10%;

    --popover: 210 15% 96%;
    --popover-foreground: 220 25% 10%;

    /* Primary gaming colors - Bright Electric Blue */
    --primary: 217 95% 55%;
    --primary-foreground: 210 20% 98%;

    /* Secondary gaming colors - Vibrant Purple */
    --secondary: 262 60% 50%;
    --secondary-foreground: 210 20% 98%;

    /* Gaming accent - Bright Cyan */
    --accent: 180 100% 45%;
    --accent-foreground: 220 25% 12%;

    /* Success - Vibrant Green */
    --success: 142 75% 40%;
    --success-foreground: 210 20% 98%;

    /* Warning - Bold Orange */
    --warning: 35 95% 50%;
    --warning-foreground: 220 25% 12%;

    --muted: 220 15% 90%;
    --muted-foreground: 220 20% 40%;

    --destructive: 0 90% 55%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 18% 80%;
    --input: 220 18% 88%;
    --ring: 217 95% 55%;

    --radius: 0.875rem;

    --sidebar-background: 210 15% 94%;
    --sidebar-foreground: 220 25% 12%;
    --sidebar-primary: 217 95% 55%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 220 15% 88%;
    --sidebar-accent-foreground: 220 25% 12%;
    --sidebar-border: 220 18% 75%;
    --sidebar-ring: 217 95% 55%;
  }

  .dark {
    /* Enhanced dark mode for gaming with superior contrast */
    --background: 215 30% 4%;
    --foreground: 210 15% 96%;

    --card: 215 25% 7%;
    --card-foreground: 210 15% 95%;

    --popover: 215 25% 7%;
    --popover-foreground: 210 15% 95%;

    /* Ultra-bright gaming colors for dark mode */
    --primary: 217 95% 70%;
    --primary-foreground: 215 30% 4%;

    --secondary: 262 65% 60%;
    --secondary-foreground: 215 30% 4%;

    --accent: 180 100% 65%;
    --accent-foreground: 215 30% 4%;

    --success: 142 80% 60%;
    --success-foreground: 215 30% 4%;

    --warning: 35 100% 65%;
    --warning-foreground: 215 30% 4%;

    --muted: 215 20% 12%;
    --muted-foreground: 210 10% 75%;

    --destructive: 0 90% 70%;
    --destructive-foreground: 215 30% 4%;

    --border: 215 20% 18%;
    --input: 215 20% 15%;
    --ring: 217 95% 70%;

    --sidebar-background: 215 25% 6%;
    --sidebar-foreground: 210 15% 96%;
    --sidebar-primary: 217 95% 70%;
    --sidebar-primary-foreground: 215 30% 4%;
    --sidebar-accent: 215 20% 12%;
    --sidebar-accent-foreground: 210 15% 95%;
    --sidebar-border: 215 20% 18%;
    --sidebar-ring: 217 95% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 500;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced gaming typography */
  .font-gaming {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-weight: 700;
    letter-spacing: 0.02em;
  }

  .font-title {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 800;
    letter-spacing: -0.025em;
    line-height: 1.1;
  }

  /* Enhanced gaming effects */
  .glow-effect {
    box-shadow: 
      0 0 20px hsl(var(--primary) / 0.4),
      0 0 40px hsl(var(--primary) / 0.2);
  }

  .glow-text {
    text-shadow: 
      0 0 10px hsl(var(--primary) / 0.6),
      0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .glow-accent {
    text-shadow: 
      0 0 8px hsl(var(--accent) / 0.8),
      0 2px 4px rgba(0, 0, 0, 0.4);
  }

  /* Maximum readability classes - Ultra aggressive text contrast */
  .text-ultra-contrast {
    color: hsl(var(--foreground)) !important;
    font-weight: 900 !important;
    text-shadow: 
      0 0 2px rgba(0, 0, 0, 1) !important,
      0 1px 3px rgba(0, 0, 0, 0.9) !important,
      0 2px 8px rgba(0, 0, 0, 0.8) !important;
  }

  .text-high-contrast {
    color: hsl(var(--foreground)) !important;
    font-weight: 800 !important;
    line-height: 1.5;
    text-shadow: 
      0 0 1px rgba(0, 0, 0, 0.9) !important,
      0 1px 2px rgba(0, 0, 0, 0.7) !important;
  }

  .text-readable {
    color: hsl(var(--foreground)) !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6) !important;
  }

  .text-critical {
    color: hsl(var(--foreground)) !important;
    font-weight: 900 !important;
    font-size: 1.1em !important;
    text-shadow: 
      0 0 3px rgba(0, 0, 0, 1) !important,
      0 2px 4px rgba(0, 0, 0, 0.8) !important;
  }

  .bg-game-card {
    background: linear-gradient(
      135deg, 
      hsl(var(--card) / 0.95) 0%, 
      hsl(var(--card) / 0.85) 100%
    );
    border: 2px solid hsl(var(--primary) / 0.3);
    backdrop-filter: blur(12px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  .bg-game-card-alt {
    background: linear-gradient(
      135deg, 
      hsl(var(--muted) / 0.8) 0%, 
      hsl(var(--card) / 0.9) 100%
    );
    border: 2px solid hsl(var(--accent) / 0.4);
    backdrop-filter: blur(10px);
  }

  /* Enhanced button styling */
  .btn-game-primary {
    background: linear-gradient(
      135deg, 
      hsl(var(--primary)) 0%, 
      hsl(var(--secondary)) 100%
    );
    color: hsl(var(--primary-foreground));
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.08em;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 
      0 4px 12px hsl(var(--primary) / 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .btn-game-primary:hover {
    transform: translateY(-3px);
    box-shadow: 
      0 12px 30px hsl(var(--primary) / 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: hsl(var(--accent) / 0.5);
  }

  .btn-game-secondary {
    background: linear-gradient(
      135deg, 
      hsl(var(--secondary)) 0%, 
      hsl(var(--accent)) 100%
    );
    color: hsl(var(--secondary-foreground));
    font-weight: 700;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .btn-game-success {
    background: linear-gradient(
      135deg, 
      hsl(var(--success)) 0%, 
      hsl(var(--accent)) 100%
    );
    color: hsl(var(--success-foreground));
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  /* Enhanced input styling */
  .input-game {
    background: hsl(var(--input) / 0.8);
    border: 2px solid hsl(var(--border));
    color: hsl(var(--foreground));
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .input-game:focus {
    border-color: hsl(var(--primary));
    box-shadow: 
      0 0 0 4px hsl(var(--primary) / 0.15),
      inset 0 2px 4px rgba(0, 0, 0, 0.1);
    background: hsl(var(--input));
    color: hsl(var(--foreground));
  }

  .input-game::placeholder {
    color: hsl(var(--muted-foreground));
    opacity: 0.9;
    font-weight: 500;
  }

  .textarea-game {
    background: hsl(var(--input) / 0.8);
    border: 2px solid hsl(var(--border));
    color: hsl(var(--foreground));
    font-weight: 500;
    line-height: 1.6;
    resize: vertical;
    min-height: 120px;
  }

  .textarea-game:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.15);
    background: hsl(var(--input));
  }

  /* Enhanced score and stats styling */
  .score-display {
    background: linear-gradient(
      135deg, 
      hsl(var(--success)) 0%, 
      hsl(var(--accent)) 100%
    );
    color: hsl(var(--success-foreground));
    font-weight: 900;
    text-align: center;
    padding: 0.75rem 1.5rem;
    border-radius: calc(var(--radius) + 2px);
    font-family: 'JetBrains Mono', monospace;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border: 2px solid hsl(var(--success) / 0.3);
    box-shadow: 0 4px 12px hsl(var(--success) / 0.3);
  }

  .participant-card {
    background: linear-gradient(
      135deg, 
      hsl(var(--card) / 0.95) 0%, 
      hsl(var(--muted) / 0.8) 100%
    );
    border: 2px solid hsl(var(--primary) / 0.2);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
  }

  .participant-card:hover {
    border-color: hsl(var(--primary) / 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Enhanced status indicators */
  .status-online {
    color: hsl(var(--success));
    font-weight: 700;
    text-shadow: 0 0 8px hsl(var(--success) / 0.5);
  }

  .status-waiting {
    color: hsl(var(--warning));
    font-weight: 700;
    text-shadow: 0 0 8px hsl(var(--warning) / 0.5);
  }

  .status-playing {
    color: hsl(var(--primary));
    font-weight: 700;
    text-shadow: 0 0 8px hsl(var(--primary) / 0.5);
  }

  .status-winner {
    color: hsl(var(--accent));
    font-weight: 800;
    text-shadow: 0 0 12px hsl(var(--accent) / 0.7);
  }

  /* Enhanced animation classes */
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-pulse-glow {
    animation: pulseGlow 1.5s ease-in-out infinite;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
    }
    to {
      box-shadow: 0 0 40px hsl(var(--primary) / 0.6);
    }
  }

  @keyframes pulseGlow {
    0%, 100% {
      text-shadow: 0 0 8px hsl(var(--accent) / 0.6);
    }
    50% {
      text-shadow: 0 0 16px hsl(var(--accent) / 0.9);
    }
  }

  /* Enhanced text readability for all elements */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 800;
    line-height: 1.2;
    color: hsl(var(--foreground));
    text-rendering: optimizeLegibility;
  }

  .text-readable {
    line-height: 1.7;
    font-weight: 700 !important;
    color: hsl(var(--foreground)) !important;
    text-rendering: optimizeLegibility;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  }

  /* Ensure maximum contrast for all text elements */
  p, span, div, label, input, button, textarea {
    color: hsl(var(--foreground)) !important;
    font-weight: 600 !important;
  }

  /* Ultra-high contrast for critical text */
  .text-critical {
    color: hsl(var(--foreground));
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .text-emphasis {
    font-weight: 800;
    color: hsl(var(--primary));
    text-shadow: 0 0 8px hsl(var(--primary) / 0.5);
  }

  .text-success {
    color: hsl(var(--success));
    font-weight: 700;
    text-shadow: 0 0 6px hsl(var(--success) / 0.4);
  }

  .text-warning {
    color: hsl(var(--warning));
    font-weight: 700;
    text-shadow: 0 0 6px hsl(var(--warning) / 0.4);
  }

  .text-error {
    color: hsl(var(--destructive));
    font-weight: 700;
    text-shadow: 0 0 6px hsl(var(--destructive) / 0.4);
  }

  .text-accent {
    color: hsl(var(--accent));
    font-weight: 700;
    text-shadow: 0 0 8px hsl(var(--accent) / 0.5);
  }

  /* Gaming-specific text styles */
  .text-game-title {
    font-family: 'Inter', system-ui, sans-serif;
    font-weight: 900 !important;
    letter-spacing: -0.02em;
    color: hsl(var(--foreground)) !important;
    text-shadow: 
      0 0 20px hsl(var(--primary) / 0.6),
      0 4px 8px rgba(0, 0, 0, 0.5) !important;
  }

  .text-game-subtitle {
    font-weight: 800 !important;
    color: hsl(var(--foreground)) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
  }

  /* Card content text enhancement */
  .card-text {
    color: hsl(var(--card-foreground));
    font-weight: 600;
    line-height: 1.6;
  }

  .card-title {
    color: hsl(var(--card-foreground));
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  /* Interactive element text */
  button {
    font-weight: 700;
    letter-spacing: 0.025em;
  }

  input, textarea {
    font-weight: 600;
    color: hsl(var(--foreground)) !important;
  }

  input::placeholder, textarea::placeholder {
    color: hsl(var(--muted-foreground)) !important;
    opacity: 0.8;
    font-weight: 500;
  }

  /* Progress and timer text */
  .timer-text {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 800;
    font-size: 1.25rem;
    color: hsl(var(--warning));
    text-shadow: 0 0 10px hsl(var(--warning) / 0.6);
  }

  .progress-text {
    font-weight: 700;
    color: hsl(var(--primary));
    text-shadow: 0 0 6px hsl(var(--primary) / 0.4);
  }

  /* GLOBAL TEXT CONTRAST ENFORCEMENT - Maximum readability */
  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground)) !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
  }

  p, span, div, label {
    color: hsl(var(--foreground)) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  }

  /* Button text contrast */
  button * {
    color: inherit !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6) !important;
  }

  /* Link text contrast */
  a {
    color: hsl(var(--primary)) !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  }

  /* Card and container text */
  .card, .card * {
    font-weight: 600 !important;
  }

  /* Specific gaming text elements */
  .text-sm {
    font-weight: 600 !important;
    color: hsl(var(--foreground)) !important;
  }

  .text-lg {
    font-weight: 700 !important;
    color: hsl(var(--foreground)) !important;
  }

  .text-xl {
    font-weight: 800 !important;
    color: hsl(var(--foreground)) !important;
  }

  .text-2xl, .text-3xl, .text-4xl {
    font-weight: 900 !important;
    color: hsl(var(--foreground)) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
  }
}