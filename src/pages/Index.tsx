
import React, { useState } from 'react';
import Registration from '@/components/Registration';
import WaitingRoom from '@/components/WaitingRoom';
import ChallengeArena from '@/components/ChallengeArena';
import FinalResults from '@/components/FinalResults';

export type GamePhase = 'registration' | 'waiting' | 'arena' | 'results';

export interface Participant {
  id: string;
  name: string;
  score: number;
  isHost?: boolean;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  timeLimit: number;
  type: 'prompt' | 'human-ai' | 'errors' | 'prompt-choice';
}

const Index = () => {
  const [currentPhase, setCurrentPhase] = useState<GamePhase>('registration');
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [currentUser, setCurrentUser] = useState<Participant | null>(null);

  const handleRegistration = (name: string) => {
    const newParticipant: Participant = {
      id: Date.now().toString(),
      name,
      score: 0,
      isHost: participants.length === 0
    };
    setParticipants(prev => [...prev, newParticipant]);
    setCurrentUser(newParticipant);
    setCurrentPhase('waiting');
  };

  const startGame = () => {
    setCurrentPhase('arena');
  };

  const endGame = () => {
    setCurrentPhase('results');
  };

  const resetGame = () => {
    setCurrentPhase('registration');
    setParticipants([]);
    setCurrentUser(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-6xl md:text-7xl font-title bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent mb-4 text-game-title animate-pulse-glow text-ultra-contrast">
            🚀 PROMPT BATTLE
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 font-bold text-game-subtitle text-ultra-contrast">
            Desafíos épicos de IA • Compite • Aprende • Gana
          </p>
          <div className="mt-4 text-sm md:text-base text-gray-300 font-semibold text-ultra-contrast">
            ⚡ La arena definitiva para maestros de prompts ⚡
          </div>
        </div>

        {/* Phase Content */}
        {currentPhase === 'registration' && (
          <Registration onRegister={handleRegistration} />
        )}

        {currentPhase === 'waiting' && (
          <WaitingRoom 
            participants={participants}
            currentUser={currentUser}
            onStartGame={startGame}
          />
        )}

        {currentPhase === 'arena' && (
          <ChallengeArena 
            participants={participants}
            setParticipants={setParticipants}
            onGameEnd={endGame}
            currentUser={currentUser}
          />
        )}

        {currentPhase === 'results' && (
          <FinalResults 
            participants={participants}
            onResetGame={resetGame}
          />
        )}
      </div>
    </div>
  );
};

export default Index;
