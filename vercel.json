{"framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NODE_ENV": "production"}, "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}